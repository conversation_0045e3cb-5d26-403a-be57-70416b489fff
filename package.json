{"name": "layeredge-edgen-community", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "node scripts/build-windows.js", "build:original": "prisma generate && prisma migrate deploy && next build", "build:windows": "node scripts/build-windows.js", "start": "next start", "lint": "next lint", "clean": "node scripts/clean.js", "clean:all": "node scripts/clean.js && rm -rf node_modules && npm install", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:seed": "tsx scripts/seed.ts", "db:migrate": "prisma migrate deploy", "db:verify": "tsx scripts/verify-database.ts", "postinstall": "prisma generate"}, "dependencies": {"@headlessui/react": "^2.0.0", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.7.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "axios": "^1.6.2", "cheerio": "^1.0.0", "clsx": "^2.0.0", "date-fns": "^3.0.0", "framer-motion": "^11.0.0", "next": "15.3.2", "next-auth": "^4.24.5", "playwright": "^1.52.0", "prisma": "^5.7.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.48.2", "ws": "^8.14.2", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.5.10", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}